@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  /* Cores específicas do Veritas baseadas no Figma */
  --veritas-purple-primary: #351397;
  --veritas-purple-secondary: #844ae9;
  --veritas-purple-light: #a855f7;
  --veritas-blue-dark: #102148;
  --veritas-gray-light: #f7f7f7;
  --veritas-gray-border: #d7d4de;
  --veritas-results-background: #f5f5f5;
  
  /* Cores de estado */
  --veritas-success: #10b981;
  --veritas-warning: #f59e0b;
  --veritas-error: #ef4444;
  --veritas-info: #3b82f6;

  /* === Design v0.2 - Tokens de Cores === */
  
  /* Cores Primárias (Purple) */
  --purple-primary: #351397;
  --purple-accent: #731fb8;
  --purple-button: #844ae9;
  --purple-link: #5d13dd;
  --purple-hover: #8a5cda;
  --purple-disabled: #b58ef6;
  --purple-dark: #381257;
  --purple-medium: #553484;

  /* Cores de Estado (v0.2) */
  --success-green: #22c55e;
  --warning-amber: #aa710e;
  --error-red: #ef4444;
  --error-background: #fff5f5;
  --validating-purple: #8a5cda;

  /* Cores Neutras (v0.2) */
  --bg-gradient-start: #f5f5f5;
  --bg-gradient-end: #eaeaea;
  --bg-overlay: #f1f1f1;
  --card-bg: #f2f2f2;
  --card-border: #b8b8b8;
  --input-bg: #ffffff;
  --input-bg-secondary: #f8f8f8;
  --helper-border: #d6d8db;
  --divider: #d7d4de;
  --coming-soon-bg: #f1f1f1;
  --coming-soon-border: #dedfe3;

  /* Cores de Texto (v0.2) */
  --text-primary: #102148;
  --text-secondary: #1e293b;
  --text-title: #60526c;
  --text-tertiary: #6c6688;
  --text-muted: #6e678a;
  --text-helper: #716793;
  --text-placeholder: #717070;
  --text-active: #383838;
  --text-tagline: #737b91;
  --text-helper-expanded: #64748b;

  /* Cores do sistema baseadas no Veritas */
  --background: #f8fafc;
  --foreground: #1e293b;
  --card: #ffffff;
  --card-foreground: #1e293b;
  --popover: #ffffff;
  --popover-foreground: #1e293b;
  --primary: var(--veritas-purple-primary);
  --primary-foreground: #ffffff;
  --secondary: #e5e7eb;
  --secondary-foreground: #374151;
  --muted: var(--veritas-gray-light);
  --muted-foreground: #6b7280;
  --accent: #e0e7ff;
  --accent-foreground: #374151;
  --destructive: var(--veritas-error);
  --destructive-foreground: #ffffff;
  --border: var(--veritas-gray-border);
  --input: var(--veritas-gray-border);
  --ring: var(--veritas-purple-primary);
  --chart-1: var(--veritas-purple-primary);
  --chart-2: var(--veritas-purple-secondary);
  --chart-3: var(--veritas-purple-light);
  --chart-4: #4338ca;
  --chart-5: #312e81;
  --sidebar: var(--veritas-gray-light);
  --sidebar-foreground: #1e293b;
  --sidebar-primary: var(--veritas-purple-primary);
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #e0e7ff;
  --sidebar-accent-foreground: #374151;
  --sidebar-border: var(--veritas-gray-border);
  --sidebar-ring: var(--veritas-purple-primary);
  /* Fontes do sistema (sem Google Fonts devido a bloqueio de rede) */
  --font-sans: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  --font-serif: Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: ui-monospace, SFMono-Regular, "SF Mono", Menlo, Consolas, "Liberation Mono", monospace;
  /* Fallbacks for Ladle/Vite when next/font variables are not injected */
  --font-inter: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-inria-serif: Georgia, Cambria, "Times New Roman", serif;
  --font-merriweather: Georgia, Cambria, "Times New Roman", serif;
  --radius: 0.5rem;
  --shadow-2xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10);
  --shadow: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10);
  --shadow-md: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 2px 4px -2px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 4px 6px -2px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 8px 10px -2px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.25);
  --tracking-normal: 0em;
  --spacing: 0.25rem;

  /* === Cores de Skeleton Loading === */
  --skeleton-bg-light: #e7ebeb;
  --skeleton-bg-medium: #cbd0d5;

  /* === Cores de Texto Específicas === */
  --text-error: #d44a4a;
  --text-error-bg: rgba(212, 74, 74, 0.15);

  /* === Cores de Bordas === */
  --border-card: #d9d9d9;
  --border-analysis: #d8d8d8;
  --border-hover-purple: #b09ac5;

  /* === Backgrounds === */
  --bg-analysis: rgba(245, 245, 245, 0.5);
  --bg-purple-light: rgba(132, 74, 233, 0.1);

  /* === Opacidades de Texto === */
  --text-opacity-dark: rgba(29, 30, 31, 0.9);

  /* === Cores de Links === */
  --link-hover: #5d13dd;

  /* === Gradientes === */
  --gradient-bg-start: #f5f5f5;
  --gradient-bg-end: #eaeaea;

  /* === Sombras Específicas === */
  --shadow-card: 0px 20px 80px 0px rgba(137, 44, 219, 0.2);
  --shadow-hover-accordion: 0 0 10px 0 rgba(0, 0, 0, 0.10);
}

.dark {
  /* Cores específicas do Veritas para tema dark */
  --veritas-purple-primary: #844ae9;
  --veritas-purple-secondary: #a855f7;
  --veritas-purple-light: #c084fc;
  --veritas-blue-dark: #1e293b;
  --veritas-gray-light: #1e293b;
  --veritas-gray-border: #4b5563;
  --veritas-results-background: #0f172a;
  
  /* Cores de estado para dark */
  --veritas-success: #10b981;
  --veritas-warning: #f59e0b;
  --veritas-error: #ef4444;
  --veritas-info: #3b82f6;

  /* Cores do sistema dark baseadas no Veritas */
  --background: #0f172a;
  --foreground: #e2e8f0;
  --card: #1e293b;
  --card-foreground: #e2e8f0;
  --popover: #1e293b;
  --popover-foreground: #e2e8f0;
  --primary: var(--veritas-purple-secondary);
  --primary-foreground: #0f172a;
  --secondary: #2d3748;
  --secondary-foreground: #d1d5db;
  --muted: var(--veritas-gray-light);
  --muted-foreground: #9ca3af;
  --accent: #374151;
  --accent-foreground: #d1d5db;
  --destructive: var(--veritas-error);
  --destructive-foreground: #0f172a;
  --border: var(--veritas-gray-border);
  --input: var(--veritas-gray-border);
  --ring: var(--veritas-purple-secondary);
  --chart-1: var(--veritas-purple-secondary);
  --chart-2: var(--veritas-purple-light);
  --chart-3: #4f46e5;
  --chart-4: #4338ca;
  --chart-5: #3730a3;
  --sidebar: var(--veritas-gray-light);
  --sidebar-foreground: #e2e8f0;
  --sidebar-primary: var(--veritas-purple-secondary);
  --sidebar-primary-foreground: #0f172a;
  --sidebar-accent: #374151;
  --sidebar-accent-foreground: #d1d5db;
  --sidebar-border: var(--veritas-gray-border);
  --sidebar-ring: var(--veritas-purple-secondary);
  /* Fontes do sistema (sem Google Fonts devido a bloqueio de rede) */
  --font-sans: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  --font-serif: Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: ui-monospace, SFMono-Regular, "SF Mono", Menlo, Consolas, "Liberation Mono", monospace;
  /* Fallbacks for Ladle/Vite when next/font variables are not injected */
  --font-inter: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-inria-serif: Georgia, Cambria, "Times New Roman", serif;
  --font-merriweather: Georgia, Cambria, "Times New Roman", serif;
  --radius: 0.5rem;
  --shadow-2xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10);
  --shadow: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10);
  --shadow-md: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 2px 4px -2px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 4px 6px -2px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 8px 10px -2px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.25);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Força fonte serif no textarea placeholder */
textarea[data-slot="textarea"]::placeholder {
  font-family: Georgia, Cambria, "Times New Roman", serif !important;
  font-size: 16px !important;
}

/* Estado disabled - placeholder com opacidade reduzida */
textarea[data-slot="textarea"]:disabled::placeholder {
  opacity: 0.65 !important;
}

/* Animação shimmer para skeleton loading */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Animação fadePulse para círculo de progresso */
@keyframes fadePulse {
  0% {
    background-color: #77e6a0;
  }
  50% {
    background-color: #f5f5f5;
  }
  100% {
    background-color: #77e6a0;
  }
}
