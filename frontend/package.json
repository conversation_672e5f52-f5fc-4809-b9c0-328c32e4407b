{"name": "frontend", "version": "0.1.0", "private": true, "engines": {"node": ">= 22.11 <= 23"}, "packageManager": "pnpm@10.18.1", "scripts": {"dev": "next dev --turbopack --port 3001", "build": "next build --turbopack", "start": "next start --port 3001", "lint": "eslint", "ladle:dev": "ladle dev", "ladle:build": "ladle build", "ladle:preview": "ladle preview"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-tooltip": "^1.2.8", "@veritas/shared": "workspace:*", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "lucide-react": "^0.541.0", "next": "15.5.0", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "socket.io-client": "^4.8.1", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@eslint/js": "~9.17.0", "@ladle/react": "^5.0.3", "@tailwindcss/postcss": "^4", "@types/crypto-js": "^4.2.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.46.0", "@typescript-eslint/parser": "~8.46.0", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^6.1.1", "tailwindcss": "^4", "tw-animate-css": "^1.3.7", "typescript": "catalog:"}}