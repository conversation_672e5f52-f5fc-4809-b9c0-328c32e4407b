{"name": "node-typescript-boilerplate", "version": "0.0.0", "description": "Minimalistic boilerplate to quick-start Node.js development in TypeScript.", "type": "module", "engines": {"node": ">= 22.11 <= 23"}, "packageManager": "pnpm@10.18.1", "scripts": {"start": "node build/src/main.js", "dev": "node --import tsx src/main.ts", "dev:watch": "node --import tsx --watch src/main.ts", "clean": "rimraf coverage build tmp", "prebuild": "pnpm lint", "build": "tsc -p tsconfig.json && tsc-alias -p tsconfig.alias.json", "build:watch": "tsc -w -p tsconfig.json", "build:release": "pnpm clean && tsc -p tsconfig.release.json && tsc-alias -p tsconfig.alias.json", "lint": "eslint .", "test": "vitest run unit --config __tests__/vitest.config.ts", "test:coverage": "vitest run unit --config __tests__/vitest.config.ts --coverage.enabled --coverage.all", "prettier": "prettier \"{src,__{tests}__}/**/*.{ts,mts}\" --config .prettierrc --write", "prettier:check": "prettier \"{src,__{tests}__}/**/*.{ts,mts}\" --config .prettierrc --check", "test:watch": "vitest unit"}, "devDependencies": {"@eslint/js": "~9.17", "@stylistic/eslint-plugin": "^5.2.3", "@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/eslint__js": "~8.42", "@types/express": "^5.0.3", "@types/node": "~20.19.11", "@typescript-eslint/parser": "~8.19", "@vitest/coverage-v8": "~2.1", "@vitest/eslint-plugin": "~1.1", "eslint": "~9.17", "eslint-config-prettier": "~9.1", "globals": "~15.14", "prettier": "~3.4", "rimraf": "~6.0", "ts-api-utils": "~2.0", "ts-node": "^10.9.2", "tsc-alias": "^1.8.16", "tsx": "^4.20.5", "typescript": "catalog:", "typescript-eslint": "~8.19", "vitest": "~2.1"}, "license": "Apache-2.0", "dependencies": {"@langchain/community": "^0.3.53", "@langchain/core": "^0.3.72", "@langchain/google-genai": "^0.2.16", "@langchain/openai": "^0.6.11", "@langchain/tavily": "^0.1.5", "compression": "^1.8.1", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "helmet": "^8.1.0", "langchain": "^0.3.31", "socket.io": "^4.8.1", "tslib": "~2.8", "winston": "^3.17.0", "zod": "^4.1.4"}, "volta": {"node": "22.12.0"}}